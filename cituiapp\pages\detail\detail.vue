<template>
	<view class="page-container">
		<!-- 固定顶部导航 -->
		<view class="fixed-header">
			<view class="header-content">
				<view class="back-btn" @click="goBack">
					<text class="back-icon">‹</text>
				</view>
				<view class="header-title">详情</view>
				<view class="header-placeholder"></view>
			</view>
		</view>
		
		<!-- 页面内容区域 -->
		<view class="content-container">
			<scroll-view 
				class="scroll-container"
				scroll-y
				refresher-enabled
				:refresher-triggered="isRefreshing"
				@refresherrefresh="onRefresh"
				@refresherrestore="onRestore"
			>
				<view class="content-inner">
					<!-- 头部信息 -->
					<view class="app-info-section">
						<view class="app-info-card">
							<image 
								:src="appInfo.logo" 
								class="app-logo"
								mode="aspectFill"
							></image>
							<view class="app-details">
								<view class="app-header">
									<text class="app-name">{{ appInfo.name }}</text>
									<view class="download-count">
										<text class="download-icon">⬇</text>
										<text class="count-text">{{ appInfo.downloadCount }}次</text>
									</view>
								</view>
								<view class="app-meta">
									<view class="rating-info">
										<text class="star-icon">⭐</text>
										<text class="rating-text">{{ appInfo.rating }}分（{{ appInfo.type }}）</text>
									</view>
								</view>
								<view class="tags-container">
									<view 
										v-for="tag in appInfo.tags" 
										:key="tag.text"
										class="tag"
										:class="getTagClass(tag.type)"
									>
										<text class="tag-text">{{ tag.emoji }}{{ tag.text }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 数据看板 -->
					<view class="data-section">
						<view class="section-card">
							<text class="section-title">测试数据报告</text>
							
							<!-- 数据统计网格 -->
							<view class="data-grid">
								<view class="data-item">
									<text class="data-label">测试条数</text>
									<text class="data-value">{{ testData.testCount }}条</text>
								</view>
								<view class="data-item">
									<text class="data-label">测试收益</text>
									<text class="data-value">¥{{ testData.testEarnings }}</text>
								</view>
								<view class="data-item">
									<text class="data-label">测试时长</text>
									<text class="data-value">{{ testData.testDuration }}分钟</text>
								</view>
								<view class="data-item">
									<text class="data-label">测试设备</text>
									<text class="data-value">{{ testData.testDevice }}</text>
								</view>
							</view>
							
							<!-- 购买同款设备按钮 -->
							<view class="device-btn" @click="buyDevice">
								<text class="device-btn-text">购买同款设备</text>
							</view>
							
							<!-- 三重验证真机实测 -->
							<view class="verification-section">
								<text class="verification-title">三重验证真机实测</text>
								<view class="verification-grid">
									<view 
										v-for="(item, index) in verificationImages" 
										:key="index"
										class="verification-item"
									>
										<image 
											:src="item.image" 
											class="verification-image"
											mode="aspectFill"
										></image>
										<text class="verification-label">{{ item.label }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 测评报告 -->
					<view class="report-section">
						<view class="section-card">
							<view class="report-header">
								<text class="lock-icon">🔒</text>
								<text class="section-title">测评报告</text>
							</view>
							<view class="report-meta">
								<text class="report-time">评测时间：{{ reportInfo.evaluationTime }}</text>
								<text class="report-author">评测人：{{ reportInfo.evaluator }}</text>
							</view>
							<view class="report-content">
								<text class="report-text">{{ reportInfo.content }}</text>
							</view>
						</view>
					</view>

					<!-- 放水记录 -->
					<view class="water-section">
						<view class="section-card">
							<view class="water-header">
								<view class="water-title-group">
									<text class="water-icon">💧</text>
									<text class="section-title">放水记录</text>
								</view>
								<view class="submit-clue-btn" @click="submitClue">
									<text class="plus-icon">+</text>
									<text class="submit-text">提交放水线索赚积分</text>
								</view>
							</view>
							
							<!-- 放水记录列表 -->
							<view class="water-records">
								<view 
									v-for="(record, index) in waterRecords" 
									:key="record.id"
									class="water-record-item"
									:class="{ 'border-bottom': index < waterRecords.length - 1 }"
								>
									<view class="user-avatar">
										<image 
											:src="record.avatar" 
											class="avatar-image"
											mode="aspectFill"
										></image>
									</view>
									<view class="record-content">
										<view class="record-header">
											<view class="user-info">
												<text class="user-name">{{ record.userName }}</text>
												<text 
													class="verify-status"
													:class="record.isVerified ? 'verified' : 'unverified'"
												>
													[{{ record.isVerified ? '已实名' : '未实名' }}]
												</text>
											</view>
											<view 
												class="amount-tag"
												:class="getAmountTagClass(record.amount)"
											>
												<text class="amount-text">放水{{ record.amount }}元</text>
											</view>
										</view>
										<text class="record-description">{{ record.description }}</text>
										<view class="record-footer">
											<text class="device-info">{{ record.device }}</text>
											<text class="record-time">{{ record.time }}</text>
										</view>
										<!-- 截图展示 -->
										<view v-if="record.screenshots && record.screenshots.length > 0" class="screenshots">
											<image 
												v-for="(screenshot, idx) in record.screenshots" 
												:key="idx"
												:src="screenshot" 
												class="screenshot-image"
												mode="aspectFill"
											></image>
										</view>
									</view>
								</view>
							</view>
							
							<!-- 查看更多记录按钮 -->
							<view class="load-more-container" v-if="showLoadMore">
								<view 
									class="load-more-btn" 
									@click="loadMoreRecords"
									:class="{ 'loading': isLoading }"
								>
									<text class="load-more-text">
										{{ isLoading ? '加载中...' : hasMoreData ? '查看更多记录' : '没有更多记录了' }}
									</text>
									<text v-if="!isLoading && hasMoreData" class="chevron-icon">›</text>
								</view>
							</view>
						</view>
					</view>

					<!-- 常见问题 -->
					<!-- <view class="faq-section">
						<view class="section-card">
							<view class="faq-header">
								<text class="help-icon">❓</text>
								<text class="section-title">常见问题</text>
							</view>
							<view class="faq-list">
								<view 
									v-for="(faq, index) in faqList" 
									:key="index"
									class="faq-item"
									:class="{ 'border-bottom': index < faqList.length - 1 }"
								>
									<text class="faq-question">{{ faq.question }}</text>
									<text class="faq-answer">{{ faq.answer }}</text>
								</view>
							</view>
							<view class="more-faq-btn" @click="viewMoreFAQ">
								<text class="more-faq-text">更多问题解答</text>
								<text class="chevron-icon">›</text>
							</view>
						</view>
					</view> -->
					
					<!-- 底部安全区域 -->
					<view class="safe-area-bottom"></view>
				</view>
			</scroll-view>
		</view>
		
		<!-- 底部行动按钮 -->
		<!-- <view class="action-button-container">
			<view class="action-button" @click="startEarning">
				<text class="action-button-text">立即赚钱</text>
			</view>
		</view> -->
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isRefreshing: false,
				isLoading: false,
				showLoadMore: true,
				hasMoreData: true,
				currentPage: 1,
				pageSize: 5,
				
				// APP信息
				appInfo: {
					id: 1,
					name: '金币大师',
					type: '合成小游戏',
					rating: 4.7,
					downloadCount: 3560,
					logo: 'https://placehold.co/200x200',
					tags: [
						{ text: '自动', type: 'red', emoji: '🔥' },
						{ text: '新人￥0.25', type: 'amber', emoji: '⚡' },
						{ text: '￥0.1起提', type: 'amber', emoji: '' },
						{ text: '顶包￥2', type: 'blue', emoji: '' }
					]
				},
				
				// 测试数据
				testData: {
					testCount: 10,
					testEarnings: '2.5',
					testDuration: 6,
					testDevice: '华为mate30 5G'
				},
				
				// 验证图片
				verificationImages: [
					{
						image: 'https://placehold.co/200x300/FFD700/333333?text=游戏主界面',
						label: '游戏主界面'
					},
					{
						image: 'https://placehold.co/200x300/FFD700/333333?text=APP提现记录',
						label: 'APP内提现记录'
					},
					{
						image: 'https://placehold.co/200x300/FFD700/333333?text=微信到账',
						label: '微信到账记录'
					}
				],
				
				// 报告信息
				reportInfo: {
					evaluationTime: '2025-05-09',
					evaluator: '陈评测',
					content: '每天10-12点收益翻倍，分享给2好友解锁无限提现'
				},
				
				// 放水记录
				waterRecords: [],
				
				// 常见问题
				faqList: [
					{
						question: '如何让红包变大？',
						answer: '连续签到7天可获得更大红包，邀请好友也可增加红包额度。开通VIP会员后，所有红包额度提升50%。每天10-12点活动期间，红包金额翻倍。'
					},
					{
						question: '红包提现不到账如何避免？',
						answer: '建议使用实名账号，提现前完成身份验证。检查银行卡信息是否正确。部分平台首次提现可能有24小时审核期，耐心等待。若超过48小时未到账，可联系客服处理。'
					}
				]
			}
		},
		
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack()
			},
			
			// 下拉刷新
			onRefresh() {
				this.isRefreshing = true
				setTimeout(() => {
					// 重置数据并重新加载
					this.currentPage = 1
					this.hasMoreData = true
					this.loadWaterRecords(true)
					this.isRefreshing = false
				}, 1500)
			},
			
			// 刷新恢复
			onRestore() {
				this.isRefreshing = false
			},
			
			// 获取标签样式
			getTagClass(type) {
				return `tag-${type}`
			},
			
			// 获取金额标签样式
			getAmountTagClass(amount) {
				const amountNum = parseFloat(amount)
				if (amountNum >= 1.0) return 'amount-high'
				if (amountNum >= 0.5) return 'amount-medium'
				return 'amount-low'
			},
			
			// 购买同款设备
			buyDevice() {
				uni.showToast({
					title: '跳转到设备购买页面',
					icon: 'none'
				})
			},
			
			// 提交线索
			submitClue() {
				uni.showToast({
					title: '跳转到提交线索页面',
					icon: 'none'
				})
			},
			
			// 查看更多FAQ
			viewMoreFAQ() {
				uni.showToast({
					title: '查看更多问题解答',
					icon: 'none'
				})
			},
			
			// 开始赚钱
			startEarning() {
				uni.showToast({
					title: '开始赚钱',
					icon: 'success'
				})
			},
			
			// 加载放水记录
			loadWaterRecords(isRefresh = false) {
				if (isRefresh) {
					this.waterRecords = []
					this.currentPage = 1
				}
				
				// 模拟API调用
				const mockRecords = this.generateMockWaterRecords()
				
				if (isRefresh) {
					this.waterRecords = mockRecords
				} else {
					this.waterRecords = [...this.waterRecords, ...mockRecords]
				}
				
				// 检查是否还有更多数据
				if (this.currentPage >= 3) { // 模拟只有3页数据
					this.hasMoreData = false
				}
			},
			
			// 加载更多记录
			loadMoreRecords() {
				if (this.isLoading || !this.hasMoreData) {
					return
				}
				
				this.isLoading = true
				
				setTimeout(() => {
					this.currentPage++
					this.loadWaterRecords(false)
					this.isLoading = false
				}, 1000)
			},
			
			// 生成模拟放水记录数据
			generateMockWaterRecords() {
				const mockData = [
					{
						id: Date.now() + Math.random(),
						userName: '用户8273',
						isVerified: true,
						amount: '1.2',
						description: '新人秒到，实名后提现速度快',
						device: '华为mate40 Pro',
						time: '2025-05-12 11:23',
						avatar: 'https://placehold.co/40x40',
						screenshots: []
					},
					{
						id: Date.now() + Math.random() + 1,
						userName: '用户6521',
						isVerified: false,
						amount: '0.5',
						description: '首次提现需要等待3小时',
						device: 'iPhone 15 Pro',
						time: '2025-05-11 16:45',
						avatar: 'https://placehold.co/40x40',
						screenshots: [
							'https://placehold.co/200x150/FFD700/333333?text=APP提现记录',
							'https://placehold.co/200x150/90EE90/333333?text=微信到账记录'
						]
					},
					{
						id: Date.now() + Math.random() + 2,
						userName: '用户3927',
						isVerified: true,
						amount: '0.3',
						description: '老用户每日可提0.3元',
						device: '小米13',
						time: '2025-05-10 09:12',
						avatar: 'https://placehold.co/40x40',
						screenshots: [
							'https://placehold.co/200x150/FFD700/333333?text=APP提现记录',
							'https://placehold.co/200x150/90EE90/333333?text=微信到账记录'
						]
					},
					{
						id: Date.now() + Math.random() + 3,
						userName: '用户5146',
						isVerified: true,
						amount: '0.8',
						description: '放水速度很快，今天特别活动双倍奖励！',
						device: '小米13',
						time: '2025-05-09 14:30',
						avatar: 'https://placehold.co/40x40',
						screenshots: []
					},
					{
						id: Date.now() + Math.random() + 4,
						userName: '用户7621',
						isVerified: false,
						amount: '1.5',
						description: '看剧10分钟就能提现，非常良心！',
						device: 'OPPO Find X5',
						time: '2025-05-08 20:15',
						avatar: 'https://placehold.co/40x40',
						screenshots: []
					}
				]
				
				// 根据页面返回对应数量的数据
				return mockData.slice(0, this.pageSize)
			}
		},
		
		onLoad(options) {
			// 获取传递的参数
			if (options.id) {
				this.appInfo.id = options.id
			}
			if (options.name) {
				this.appInfo.name = decodeURIComponent(options.name)
			}
			
			// 初始加载放水记录
			this.loadWaterRecords(true)
		}
	}
</script>

<style lang="scss" scoped>
.page-container {
	width: 100%;
	height: 100vh;
	background-color: #f5f5f5;
}

/* 固定顶部导航 */
.fixed-header {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background-color: #ffffff;
	border-bottom: 2rpx solid #f0f0f0;
	
	.header-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 32rpx;
		min-height: 120rpx;
		
		.back-btn {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 48rpx;
			height: 48rpx;
			
			.back-icon {
				font-size: 48rpx;
				color: #4b5563;
				font-weight: bold;
			}
		}
		
		.header-title {
			font-size: 36rpx;
			font-weight: bold;
			color: #111827;
		}
		
		.header-placeholder {
			width: 48rpx;
			height: 48rpx;
		}
	}
}

/* 内容区域 */
.content-container {
	padding-top: 120rpx;
	padding-bottom: 120rpx; /* 为底部按钮留出空间 */
	height: 100vh;
	
	.scroll-container {
		height: 100%;
		
		.content-inner {
			padding: 32rpx;
		}
	}
}

/* APP信息区域 */
.app-info-section {
	margin-bottom: 32rpx;
	
	.app-info-card {
		display: flex;
		background: #ffffff;
		border-radius: 16rpx;
		padding: 32rpx;
		box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.1);
		
		.app-logo {
			width: 112rpx;
			height: 112rpx;
			border-radius: 16rpx;
			margin-right: 24rpx;
		}
		
		.app-details {
			flex: 1;
			
			.app-header {
				display: flex;
				justify-content: space-between;
				align-items: flex-start;
				margin-bottom: 16rpx;
				
				.app-name {
					font-size: 36rpx;
					font-weight: bold;
					color: #111827;
				}
				
				.download-count {
					display: flex;
					align-items: center;
					color: #6b7280;
					
					.download-icon {
						font-size: 24rpx;
						margin-right: 4rpx;
					}
					
					.count-text {
						font-size: 24rpx;
					}
				}
			}
			
			.app-meta {
				margin-bottom: 16rpx;
				
				.rating-info {
					display: flex;
					align-items: center;
					
					.star-icon {
						font-size: 24rpx;
						margin-right: 4rpx;
					}
					
					.rating-text {
						font-size: 28rpx;
						color: #f59e0b;
						font-weight: 500;
					}
				}
			}
			
			.tags-container {
				display: flex;
				flex-wrap: wrap;
				gap: 12rpx;
				
				.tag {
					border-radius: 24rpx;
					padding: 6rpx 16rpx;
					display: inline-flex;
					align-items: center;
					justify-content: center;
					min-height: 44rpx;
					
					.tag-text {
						font-size: 22rpx;
						font-weight: 500;
						line-height: 1;
					}
					
					&.tag-red {
						background-color: #fee2e2;
						
						.tag-text {
							color: #dc2626;
						}
					}
					
					&.tag-amber {
						background-color: #fef3c7;
						
						.tag-text {
							color: #d97706;
						}
					}
					
					&.tag-blue {
						background-color: #dbeafe;
						
						.tag-text {
							color: #2563eb;
						}
					}
				}
			}
		}
	}
}

/* 数据区域 */
.data-section {
	margin-bottom: 32rpx;
}

/* 报告区域 */
.report-section {
	margin-bottom: 32rpx;
}

/* 放水记录区域 */
.water-section {
	margin-bottom: 32rpx;
}

/* FAQ区域 */
.faq-section {
	margin-bottom: 32rpx;
}

/* 通用卡片样式 */
.section-card {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.1);
	
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #111827;
		margin-left: 8rpx;
	}
}

/* 数据网格 */
.data-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16rpx;
	margin: 24rpx 0;
	
	.data-item {
		background-color: #f9fafb;
		padding: 24rpx;
		border-radius: 12rpx;
		text-align: center;
		
		.data-label {
			display: block;
			font-size: 22rpx;
			color: #6b7280;
			margin-bottom: 8rpx;
		}
		
		.data-value {
			display: block;
			font-size: 28rpx;
			font-weight: 600;
			color: #111827;
		}
	}
}

/* 设备按钮 */
.device-btn {
	background: linear-gradient(135deg, #3b82f6, #2563eb);
	border-radius: 12rpx;
	padding: 24rpx;
	text-align: center;
	margin: 24rpx 0;
	
	.device-btn-text {
		color: #ffffff;
		font-size: 28rpx;
		font-weight: 600;
	}
}

/* 验证区域 */
.verification-section {
	margin-top: 40rpx;
	
	.verification-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #111827;
		margin-bottom: 24rpx;
		display: block;
	}
	
	.verification-grid {
		display: grid;
		grid-template-columns: 1fr 1fr 1fr;
		gap: 16rpx;
		
		.verification-item {
			text-align: center;
			
			.verification-image {
				width: 100%;
				height: 200rpx;
				border-radius: 12rpx;
				margin-bottom: 8rpx;
			}
			
			.verification-label {
				font-size: 22rpx;
				color: #6b7280;
				display: block;
			}
		}
	}
}

/* 报告头部 */
.report-header {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
	
	.lock-icon {
		font-size: 36rpx;
		margin-right: 12rpx;
	}
}

.report-meta {
	display: flex;
	justify-content: space-between;
	margin-bottom: 16rpx;
	padding-left: 48rpx;
	
	.report-time, .report-author {
		font-size: 22rpx;
		color: #6b7280;
	}
}

.report-content {
	padding-left: 48rpx;
	
	.report-text {
		font-size: 28rpx;
		color: #374151;
		line-height: 1.6;
	}
}

/* 放水记录头部 */
.water-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 24rpx;
	
	.water-title-group {
		display: flex;
		align-items: center;
		
		.water-icon {
			font-size: 36rpx;
			margin-right: 12rpx;
		}
	}
	
	.submit-clue-btn {
		background-color: #fef3c7;
		color: #d97706;
		padding: 8rpx 16rpx;
		border-radius: 24rpx;
		display: flex;
		align-items: center;
		
		.plus-icon {
			font-size: 20rpx;
			margin-right: 4rpx;
		}
		
		.submit-text {
			font-size: 22rpx;
			font-weight: 500;
		}
	}
}

/* 放水记录列表 */
.water-records {
	.water-record-item {
		display: flex;
		padding: 24rpx 0;
		
		&.border-bottom {
			border-bottom: 2rpx solid #f3f4f6;
		}
		
		.user-avatar {
			margin-right: 16rpx;
			
			.avatar-image {
				width: 64rpx;
				height: 64rpx;
				border-radius: 50%;
				background-color: #e5e7eb;
			}
		}
		
		.record-content {
			flex: 1;
			
			.record-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 8rpx;
				
				.user-info {
					display: flex;
					align-items: center;
					
					.user-name {
						font-size: 28rpx;
						font-weight: 500;
						color: #111827;
						margin-right: 8rpx;
					}
					
					.verify-status {
						font-size: 22rpx;
						
						&.verified {
							color: #16a34a;
						}
						
						&.unverified {
							color: #f59e0b;
						}
					}
				}
				
				.amount-tag {
					padding: 4rpx 12rpx;
					border-radius: 24rpx;
					
					.amount-text {
						font-size: 22rpx;
						font-weight: 500;
					}
					
					&.amount-high {
						background-color: #fee2e2;
						
						.amount-text {
							color: #dc2626;
						}
					}
					
					&.amount-medium {
						background-color: #fef3c7;
						
						.amount-text {
							color: #d97706;
						}
					}
					
					&.amount-low {
						background-color: #fef3c7;
						
						.amount-text {
							color: #d97706;
						}
					}
				}
			}
			
			.record-description {
				font-size: 28rpx;
				color: #374151;
				margin-bottom: 8rpx;
				line-height: 1.5;
			}
			
			.record-footer {
				display: flex;
				justify-content: space-between;
				margin-bottom: 16rpx;
				
				.device-info, .record-time {
					font-size: 22rpx;
					color: #6b7280;
				}
			}
			
			.screenshots {
				display: grid;
				grid-template-columns: 1fr 1fr;
				gap: 16rpx;
				
				.screenshot-image {
					width: 100%;
					height: 200rpx;
					border-radius: 12rpx;
				}
			}
		}
	}
}

/* 加载更多按钮 */
.load-more-container {
	text-align: center;
	margin-top: 32rpx;
	
	.load-more-btn {
		display: inline-flex;
		align-items: center;
		color: #ef4444;
		
		&.loading {
			color: #6b7280;
		}
		
		.load-more-text {
			font-size: 28rpx;
			font-weight: 500;
			margin-right: 8rpx;
		}
		
		.chevron-icon {
			font-size: 32rpx;
			font-weight: bold;
		}
	}
}

/* FAQ区域 */
.faq-header {
	display: flex;
	align-items: center;
	margin-bottom: 24rpx;
	
	.help-icon {
		font-size: 36rpx;
		margin-right: 12rpx;
	}
}

.faq-list {
	.faq-item {
		padding: 24rpx 0;
		
		&.border-bottom {
			border-bottom: 2rpx solid #f3f4f6;
		}
		
		.faq-question {
			font-size: 28rpx;
			font-weight: 500;
			color: #111827;
			margin-bottom: 16rpx;
			display: block;
		}
		
		.faq-answer {
			font-size: 24rpx;
			color: #6b7280;
			line-height: 1.6;
		}
	}
}

.more-faq-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 24rpx;
	
	.more-faq-text {
		font-size: 28rpx;
		font-weight: 500;
		color: #ef4444;
		margin-right: 8rpx;
	}
	
	.chevron-icon {
		font-size: 32rpx;
		font-weight: bold;
		color: #ef4444;
	}
}

/* 底部按钮 */
.action-button-container {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 998;
	background-color: #ffffff;
	border-top: 2rpx solid #f0f0f0;
	padding: 24rpx 32rpx;
	padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
	
	.action-button {
		background: linear-gradient(135deg, #3b82f6, #2563eb);
		border-radius: 12rpx;
		padding: 24rpx;
		text-align: center;
		box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.3);
		
		.action-button-text {
			color: #ffffff;
			font-size: 32rpx;
			font-weight: bold;
		}
	}
}

/* 底部安全区域 */
.safe-area-bottom {
	height: 120rpx;
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
	display: none;
}
</style> 